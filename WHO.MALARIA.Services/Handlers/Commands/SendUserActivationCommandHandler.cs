using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Identity;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Command handler to handle sending of user activation email using SendUserActivationCommand
    /// </summary>
    public class SendUserActivationCommandHandler : RuleBase, IRequestHandler<SendUserActivationCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IIdentityRuleChecker _identityChecker;
        private readonly IUserRuleChecker _userRuleChecker;
        private readonly IGraphService _graphService;
        private readonly ITranslationService _translationService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMediator _mediator;

        public SendUserActivationCommandHandler(IUnitOfWork unitOfWork,
            IIdentityRuleChecker identityChecker,
            IUserRuleChecker userRuleChecker,
            IGraphService graphService,
            ITranslationService translationService,
            IHttpContextAccessor httpContextAccessor,
            IMediator mediator)
        {
            _unitOfWork = unitOfWork;
            _identityChecker = identityChecker;
            _userRuleChecker = userRuleChecker;
            _graphService = graphService;
            _translationService = translationService;
            _httpContextAccessor = httpContextAccessor;
            _mediator = mediator;
        }

        /// <summary>
        /// Sends user activation email using SendUserActivationCommand
        /// </summary>
        /// <param name="request">Command includes properties related to user activation email sending</param>
        /// <param name="cancellationToken">Notify the cancellation request </param>
        /// <returns> Id of Assessment </returns>
        public async Task<bool> Handle(SendUserActivationCommand request, CancellationToken cancellationToken)
        {
            // SECURITY FIX: Authorization checks MUST come first to prevent unauthorized user activation
            // Only SuperManagers and WHO Admins can send user activation emails
            if (!_userRuleChecker.IsUserWHOAdmin(request.CurrentUserId))
            {
                CheckRule(new UserShouldBeSuperManagerRule(_translationService, _userRuleChecker, request.CurrentUserId));
            }

            // Check Business Rules (after authorization to prevent information disclosure)
            CheckRule(new ValidEmailRule(_translationService, _identityChecker, request.Email));
            CheckRule(new UserStatusShouldBePendingRule(_translationService, _userRuleChecker, request.Email));

            User user = await _unitOfWork.UserRepository.Queryable(u => u.Id == request.UserId)
                                                    .Include(u => u.Identity)
                                                    .Include(u => u.UserCountryAccesses)
                                                    .ThenInclude(uc => uc.Country)
                                                    .FirstOrDefaultAsync();

            if (user == null)
            {
                throw new RecordNotFoundException(request.UserId, "User");
            }

            //append user country access id in callback url
            UserCountryAccess userCountryAccess = user.UserCountryAccesses.Where(u => u.Id == request.UserCountryAccessId).FirstOrDefault();

            if (userCountryAccess == null)
            {
                throw new RecordNotFoundException(request.UserCountryAccessId, "UserCountryAccess");
            }

            User currentUser = await _unitOfWork.UserRepository.Queryable(u => u.Id == request.CurrentUserId)
                                                               .Include(u => u.Identity)
                                                               .SingleAsync();

            if (currentUser == null)
            {
                throw new RecordNotFoundException(request.CurrentUserId, "CurrentUser");
            }

            // Store original status for rollback if needed
            var originalStatus = userCountryAccess.Status;

            try
            {
                // Update status to InvitationNotAccepted
                userCountryAccess.Status = (int)UserCountryAccessRightsEnum.InvitationNotAccepted;
                _unitOfWork.UserCountryAccessRepository.Update(userCountryAccess);

                // Commit the status change
                if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
                {
                    throw new ApplicationException("Failed to update user country access status");
                }

                // Send the invitation email notification
                await _mediator.Publish(new UserInvitationEmailNotification(user.Name, request.Email, userCountryAccess.Country.Name, currentUser.Identity.Username, userCountryAccess.Id));

                return true;
            }
            catch (Exception ex)
            {
                // Rollback the status change if invitation failed
                try
                {
                    userCountryAccess.Status = originalStatus;
                    _unitOfWork.UserCountryAccessRepository.Update(userCountryAccess);
                    await _unitOfWork.CommitAsync(cancellationToken);
                }
                catch (Exception rollbackEx)
                {
                    // Log rollback failure but don't mask the original exception
                    // Note: In a real scenario, you might want to use a more sophisticated logging approach
                    System.Diagnostics.Debug.WriteLine($"Failed to rollback user country access status: {rollbackEx.Message}");
                    // Suppress the rollback exception to avoid masking the original issue
                    _ = rollbackEx;
                }

                // Re-throw the original exception
                throw;
            }
        }

    }
}
