﻿using MediatR;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.DocumentManager;
using WHO.MALARIA.DocumentManager.Exceptions;
using WHO.MALARIA.DocumentManager.Models;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.ServiceLevel;
using WHO.MALARIA.Features;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Handlers.Queries;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.DQA;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles DQA SL Summary command
    /// </summary>
    public class CreateOrUpdateSLSummaryCommandHandler : Rule<PERSON><PERSON>, IRequestHandler<CreateServiceLevelSummaryCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly IDQADocumentManager _dqaDocumentManager;
        private readonly IDQAQueries _dqaQueries;
        private readonly IAssessmentRuleChecker _assesmentRuleChecker;
        private readonly ICommonRuleChecker _commonRuleChecker;

        public CreateOrUpdateSLSummaryCommandHandler(IUnitOfWork unitOfWork, ITranslationService translationService, IDQADocumentManager dqaDocumentManager, IDQAQueries dqaQueries, IAssessmentRuleChecker assesmentRuleChecker, ICommonRuleChecker commonRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _dqaDocumentManager = dqaDocumentManager;
            _dqaQueries = dqaQueries;
            _assesmentRuleChecker = assesmentRuleChecker;
            _commonRuleChecker = commonRuleChecker;
        }

        /// <summary>
        /// Read service level template and add/update summary in db
        /// </summary>
        /// <param name="request">Command includes properties related to service level summary command</param>
        /// <param name="cancellationToken">Notify the cancellation request</param>
        /// <returns>Returns true if records are created else throws an error if something went wrong</returns>
        public async Task<bool> Handle(CreateServiceLevelSummaryCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.ServiceLevelId, "ServiceLevelId"));

            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessmentId"));

            CheckRule(new UserShouldHaveUploadFilePermissionRule(_translationService, _assesmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanUploadFile, Constants.Exception.NoPermissionToUpload));

            CheckRule(new IsFileNullCheckRule(_translationService, request.File, Constants.Exception.SelectFile));

            string fileExtension = "." + request.File.FileName.Split('.')[request.File.FileName.Split('.').Length - 1];
            CheckRule(new IsFileExtensionValidRule(_translationService, fileExtension, Constants.Common.ValidExcelExtensions));

            CheckRule(new FileSizeCheckRule(_translationService, request.File.Length, Constants.Common.ExcelFileSizeBytes, Constants.Exception.InvalidFileSize));

            ServiceLevelSummaryViewModel slSummaryDataModel = new ServiceLevelSummaryViewModel();
            try
            {
                slSummaryDataModel = _dqaDocumentManager.ReadSLSummarySheet(request.File);
            }
            catch (ExcelCellException ex)
            {
                Log.Error(ex, ex.ToString());
                CheckRule(new DQAFileExceptionMessageRule(_translationService.GetTranslatedMessage(Constants.Exception.TemplateEmpty)));
            }

            ServiceLevel serviceLevelData = await _dqaQueries.GetServiceLevelDataAsync(request.ServiceLevelId);

            CheckRule(new IsUploadedServiceLevelFileValidRule(_translationService, request.ServiceLevelId, slSummaryDataModel.ServiceLevelId, serviceLevelData.Version, slSummaryDataModel.Version));

            IEnumerable<DQAVariableDto> dqaVariableData = serviceLevelData.ServiceLevelVariables.
                                                          Join(serviceLevelData.ServiceLevelVariables.
                                                          Select(dqav => dqav.DQAVariable),
                                                          slv => slv.DQAVariableId, dqav => dqav.Id,
                                                          (slv, sldqav) =>
                                                          new DQAVariableDto
                                                          {
                                                              Id = slv.Id,
                                                              Name = request.Language == Constants.Common.DefaultLanguage ? sldqav.Name : sldqav.Name_FR,
                                                          }).ToList();

            Guid slVariableCompletnessId = Guid.Empty;
            // Check service level variable completeness
            if (serviceLevelData.ServiceLevelVariableCompletnesses.Any())
            {
                slVariableCompletnessId = serviceLevelData.ServiceLevelVariableCompletnesses.SingleOrDefault().Id;
            }

            IEnumerable<SLDQAVariableDataDto> slVariableData = serviceLevelData.ServiceLevelVariables.
                                                               Where(v => v.ServiceLevelVariableData != null)
                                                               .SelectMany
                                                               (
                                                                x => x.ServiceLevelVariableData?.Select
                                                               (slvd => new SLDQAVariableDataDto
                                                               {
                                                                   Id = slvd.Id,
                                                                   ServiceLevelVariableId = slvd.ServiceLevelVariableId
                                                               })
                                                               ).ToList();


            AddUpdateVariableCompletenessResponse(request, slSummaryDataModel, slVariableCompletnessId);

            AddUpdateVariableDataResponse(slSummaryDataModel, slVariableData, dqaVariableData);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            // Update file name in ServiceLevel
            await UpdateFileName(request.AssessmentId, $"{request.File.FileName} ({CommandGenerator.CalcuateFileSize(request.File.Length)})", request.CurrentUserId, cancellationToken);


            return true;
        }

        #region Private Method 

        /// <summary>
        /// Add update data in variable completness table
        /// </summary>
        /// <param name="request">Object of createOrUpdateSLSummaryCommand </param>
        /// <param name="slSummaryDataModel">Object of slSummaryDataModel</param>
        private void AddUpdateVariableCompletenessResponse(CreateServiceLevelSummaryCommand request, ServiceLevelSummaryViewModel slSummaryDataModel, Guid slVariableCompletnessId)
        {
            //Check SL Variable Completeness Id
            if (slVariableCompletnessId == Guid.Empty)
            {
                ServiceLevelVariableCompletness serviceLevelVariableCompletness = new ServiceLevelVariableCompletness(Guid.NewGuid(), request.ServiceLevelId, slSummaryDataModel.Total, slSummaryDataModel.Sex, slSummaryDataModel.Diagnosis, slSummaryDataModel.Age);

                _unitOfWork.VariableCompletenessResponseRepository.Add(serviceLevelVariableCompletness);
            }
            else
            {
                ServiceLevelVariableCompletness serviceLevelVariableCompletness = new ServiceLevelVariableCompletness(slVariableCompletnessId, request.ServiceLevelId, slSummaryDataModel.Total, slSummaryDataModel.Sex, slSummaryDataModel.Diagnosis, slSummaryDataModel.Age);

                _unitOfWork.VariableCompletenessResponseRepository.Update(serviceLevelVariableCompletness);
            }
        }

        /// <summary>
        /// Add update data in variable data table
        /// </summary>     
        /// <param name="serviceLevelSummaryViewModel">Object of ServiceLevelSummaryViewModel</param>
        /// <param name="serviceLevelVariablesData">List of service level variable data</param>
        /// <param name="serviceLevelVariables">List of service level variable</param>
        private void AddUpdateVariableDataResponse(ServiceLevelSummaryViewModel serviceLevelSummaryViewModel, IEnumerable<SLDQAVariableDataDto> serviceLevelVariablesData, IEnumerable<DQAVariableDto> serviceLevelVariables)
        {
            foreach (DQAVariableDto dqaVariableDto in serviceLevelVariables)
            {
                ServiceLevelVariableDataViewModel variableData = serviceLevelSummaryViewModel.VariableData.FirstOrDefault(variable => variable.Name.Equals(dqaVariableDto.Name, StringComparison.InvariantCultureIgnoreCase));
                if (dqaVariableDto != null)
                {
                    SLDQAVariableDataDto slDQAVariableDataDto = serviceLevelVariablesData.FirstOrDefault(vData => vData.ServiceLevelVariableId == dqaVariableDto.Id);

                    if (variableData != null && (variableData.MonthConcordance != null || variableData.ErrorInDataSource != null))
                    {
                        if (slDQAVariableDataDto == null)
                        {
                            ServiceLevelVariableData serviceLevelVariableData = new ServiceLevelVariableData(Guid.NewGuid(), dqaVariableDto.Id, variableData.MonthConcordance, variableData.ErrorInDataSource);

                            _unitOfWork.VariableDataResponseRepository.Add(serviceLevelVariableData);
                        }
                        else
                        {
                            ServiceLevelVariableData serviceLevelVariableData = new ServiceLevelVariableData(slDQAVariableDataDto.Id, dqaVariableDto.Id, variableData.MonthConcordance, variableData.ErrorInDataSource);

                            _unitOfWork.VariableDataResponseRepository.Update(serviceLevelVariableData);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Add update data in variable data table
        /// </summary>     
        /// <param name="serviceLevel">Object of ServiceLevelSummaryViewModel</param>
        /// <param name="filename">List of service level variable data</param>
        /// <param name="updatedby">List of service level variable</param>
        private async Task UpdateFileName(Guid assessmentId, string filename, Guid updatedby, CancellationToken cancellationToken)
        {

            ServiceLevel serviceLevel = await _dqaQueries.GetServiceLevelByAssessmentAsync(assessmentId);

            if (serviceLevel == null) return;

            serviceLevel.FileName = filename;

            _unitOfWork.ServiceLevelRepository.Update(serviceLevel);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }
        }

        #endregion
    }
}
